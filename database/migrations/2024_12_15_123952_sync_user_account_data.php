<?php

use App\Models\User;
use App\Models\UserAccountCredential;
use Illuminate\Database\Migrations\Migration;
use Modules\Account\app\Models\Account;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $users_without_accounts = UserAccountCredential::withoutGlobalScopes()->where(['account_id' => null])->get()->toArray();
        foreach ($users_without_accounts as $user_without_account) {
            $user = User::with('userProfile')->find($user_without_account['user_id']);
            if ($user && !empty($user->userProfile->national_id)) {
                Account::create(
                    [
                        'name' => $user->name ?? 'anonymous',
                        'first_name' => $user->userProfile->first_name ?? 'anonymous',
                        'second_name' => $user->userProfile->second_name ?? 'anonymous',
                        'third_name' => $user->userProfile->third_name ?? 'anonymous',
                        'last_name' => $user->userProfile->last_name ?? 'anonymous',
                        'email' => $this->getAccountEmail($user->email),
                        'phone' => $this->getAccountPhone($user->userProfile->phone_number) ,
                        'lang' => $user->lang ?? app()->getLocale(),
                        'password' => $user->password,
                        'national_id' => $user->userProfile->national_id,
                        'birth_date' => $user->userProfile->birth_date ?? '2000-01-01',
                        'is_active' => empty($user->email_verified_at) ? 0 : 1,
                        'otp_activated_at' => empty($user->email_verified_at) ? null : date('Y-m-d H:i:s', time()),
                    ]
                );
            }
        }
    }

    public function getAccountPhone($user_phone): string
    {
        //validate phone uniqueness
        $account_phone_exists = Account::withoutGlobalScopes()->where(['phone' => $user_phone])->first();
        if (empty($account_phone_exists) && !empty($user_phone)) {
            return $user_phone;
        } else {
            $random_phone = '+9665' . rand(********, ********);
            return $this->getAccountPhone($random_phone);
        }
    }
    public function getAccountEmail($user_email): string
    {
        ///validate email uniqueness
        $account_email_exists = Account::withoutGlobalScopes()->where(['email' => $user_email])->first();
        if (empty($account_email_exists) && !empty($user_email)) {
            return $user_email;
        } else {
            $random_email = rand(********, ********) . '@example.com';
            return $this->getAccountEmail($random_email);
        }
    }
    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //do nothing
    }
};
