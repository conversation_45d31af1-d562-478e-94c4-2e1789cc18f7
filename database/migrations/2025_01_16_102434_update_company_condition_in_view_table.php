<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
            CREATE OR REPLACE VIEW user_account_credentials AS
            SELECT
                COALESCE(u.id, NULL) as user_id,
                COALESCE(a.id, NULL) as account_id,
                COALESCE(a.national_id, up.national_id) as national_id,
                COALESCE(a.password, u.password) as password,
                CASE
                    WHEN u.id IS NOT NULL THEN u.company_id
                    ELSE NULL
                END as company_id
            FROM users u
            RIGHT JOIN user_profiles up ON u.id = up.user_id
            LEFT JOIN accounts a ON up.national_id = a.national_id AND a.deleted_at IS NULL

            UNION

            SELECT
                NULL as user_id,
                a.id as account_id,
                a.national_id,
                a.password,
                NULL as company_id
            FROM accounts a
            LEFT JOIN user_profiles up ON up.national_id = a.national_id
            WHERE up.national_id IS NULL
            AND a.deleted_at IS NULL
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::statement('DROP VIEW IF EXISTS user_account_credentials');
    }
};
