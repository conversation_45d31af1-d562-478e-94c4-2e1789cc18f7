<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\Cms\database\seeders\CMSDatabaseSeeder;
use Modules\Lease\database\seeders\LeaseSeeder;
use Modules\Lease\database\seeders\LeaseSettingSeeder;
use Modules\Notification\database\seeders\NotificationDatabaseSeeder;
use Modules\Payment\database\seeders\paymentMethodsSeeder;
use Modules\Property\database\seeders\PropertiesSeeder;
use Modules\Property\database\seeders\PropertyDatabaseSeeder;
use Modules\Property\database\seeders\UsabilitySeeder;

use Modules\Tenancy\database\seeders\TenancyDatabaseSeeder;

use Modules\Service\database\seeders\DefaultServiceSeeder;


// use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // User::factory(10)->create();

        $this->call(UsabilitySeeder::class);
        $this->call(PropertyTypeSeeder::class);
        $this->call(AttributeSeeder::class);
        $this->call(DocumentTypeSeeder::class);
        $this->call(DefaultServiceSeeder::class);
//        $this->call(PropertiesSeeder::class);
//        $this->call(LeaseSeeder::class);

//        $this->call(TenancyDatabaseSeeder::class);
        $this->call(PropertyDatabaseSeeder::class);
        $this->call(CMSDatabaseSeeder::class);

        $this->call(NotificationDatabaseSeeder::class);
//        $this->call(DefualtSubscriptionSeeder::class);
        $this->call(paymentMethodsSeeder::class);
        $this->call(LeaseSettingSeeder::class);
        $this->call(UpdateRoleSeeder::class);
        $this->call(SaudiLocationsSeeder::class);
    }
}
