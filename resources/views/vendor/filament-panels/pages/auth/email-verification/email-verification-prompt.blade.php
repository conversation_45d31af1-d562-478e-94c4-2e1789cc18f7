<x-filament-panels::page.simple>
    <p class="text-center text-sm text-gray-500 dark:text-gray-400">
        {{
            __('filament-panels::pages/auth/email-verification/email-verification-prompt.messages.notification_sent', [
                'email' => filament()->auth()->user()->getEmailForVerification(),
            ])
        }}
    </p>

    <p class="text-center text-sm text-gray-500 dark:text-gray-400">
    {{ __('filament-panels::pages/auth/email-verification/email-verification-prompt.messages.notification_not_received') }}

    @if($email = filament()->auth()->user()->getEmailForVerification())
        <div class="mt-4">
            @if($attemptCount >= 4)
                <p class="text-sm text-red-600">{{__('Maximum attempts reached. Please try again in 24 hours.')}}</p>
            @elseif($remainingTime > 0)
                <p class="text-sm text-gray-600">
                    {{__('Please Wait')}} <span x-data="{ time: {{ $remainingTime }} }"
                                                x-init="setInterval(() => {
                                          if(time > 0) time--;
                                          if(time === 0) $wire.checkResendStatus();
                                      }, 1000)"
                                                x-text="Math.floor(time / 60) + ':' + (time % 60).toString().padStart(2, '0')"></span>
                    {{__('before requesting another verification email')}}
                </p>
            @else
                {{ $this->resendNotificationAction }}
            @endif
        </div>
        @endif
        </p>
</x-filament-panels::page.simple>
