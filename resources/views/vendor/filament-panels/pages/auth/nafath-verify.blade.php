<!DOCTYPE html>
{{ app()->setLocale(session('locale', 'en')); }}
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ __('Account Nafath Not Verified') }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body, html {
            width: 100%;
            height: 100%;
        }
        .suspended-page {
            background-image: url('{{ asset('images/keraLoginBg.webp') }}');
            background-size: cover;
            background-position: center;
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            flex-direction: column;
            text-align: center;
        }
        .suspended-page h1, .suspended-page p {
            color: black;
        }
        .suspended-page h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        .suspended-page p {
            font-size: 1.5rem;
            margin-bottom: 1.5rem;
        }
        .btn {
            padding: 10px 20px;
            background-color: #205848;
            color: white;
            font-size: 1.2rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
        }
        .btn:hover {
            background-color: #205848;
        }
        
        /* Language Switcher Button Styling */
        .lang-btn {
            position: absolute;
            top: 20px;
            {{ session('locale') == 'ar' ? 'left' : 'right' }}: 20px;
            padding: 10px 15px;
            background-color: #ffffff;
            color: #205848;
            font-size: 1rem;
            border-radius: 8px;
            cursor: pointer;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        .lang-btn:hover {
            background-color: #205848;
            color: #ffffff;
        }

        /* Fixed size for the content box */
        .content-box {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(222, 238, 233, 255);
            min-width: 400px; /* Set a fixed width */
            min-height: 250px; /* Set a fixed height */
            max-width: 600px; /* Optional: set maximum width for larger screens */
        }
    </style>
</head>
<body>
    <div class="suspended-page">
        <!-- Language Switcher Button -->
        <a href="{{ route('switch.language', ['locale' => session('locale') == 'en' ? 'ar' : 'en']) }}" class="lang-btn">
            {{ session('locale') == 'ar' ? 'En' : 'ar' }}
        </a>
        
        <div class="content-box">
            <h1>
                {{ __('Your account had not been verified with nafath') }}
            </h1>
            <p>
                {{ __('If you face any issue Please contact support for assistance via') .' ' . '<EMAIL>' }}
            </p>
            <a href="javascript:void(0);" class="btn" onclick="location.replace('{{ route('nafath.send') }}')">
                {{ __('Verify With Nafath') }}
            </a>
        </div>
    </div>
</body>
</html>