<!DOCTYPE html>
{{ app()->setLocale(session('locale', 'en')); }}
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ __('Account Nafath Not Verified') }}</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body, html {
            width: 100%;
            height: 100%;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
        }
        
        .suspended-page {
            background-image: url('{{ asset('images/keraLoginBg.webp') }}');
            background-size: cover;
            background-position: center;
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            flex-direction: column;
            text-align: center;
            padding: 2rem;
        }
        
        .suspended-page h1, .suspended-page p {
            color: #1f2937;
        }
        
        .suspended-page h1 {
            font-size: 2rem;
            font-weight: 600;
            line-height: 1.3;
            margin-bottom: 1.5rem;
            letter-spacing: -0.025em;
        }
        
        .suspended-page p {
            font-size: 1rem;
            font-weight: 400;
            line-height: 1.6;
            margin-bottom: 2rem;
            color: #6b7280;
        }
        
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.875rem 1.5rem;
            background-color: #205848;
            color: white;
            font-size: 0.95rem;
            font-weight: 500;
            border: none;
            border-radius: 0.75rem;
            cursor: pointer;
            text-decoration: none;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            min-width: 120px;
        }
        
        .btn:hover {
            background-color: #1a4a3a;
            transform: translateY(-1px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }
        
        .btn:active {
            transform: translateY(0);
        }
        
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        
        /* Language Switcher Button Styling */
        .lang-btn {
            position: absolute;
            top: 2rem;
            {{ session('locale') == 'ar' ? 'left' : 'right' }}: 2rem;
            padding: 0.75rem 1rem;
            background-color: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            color: #205848;
            font-size: 0.875rem;
            font-weight: 500;
            border-radius: 0.75rem;
            cursor: pointer;
            text-decoration: none;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .lang-btn:hover {
            background-color: #205848;
            color: #ffffff;
            transform: translateY(-1px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        /* Enhanced content box */
        .content-box {
            background-color: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            padding: 3rem 2.5rem;
            border-radius: 0.75rem;
            box-shadow: 
                0 25px 50px -12px rgba(0, 0, 0, 0.25),
                0 0 0 1px rgba(255, 255, 255, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            min-width: 480px;
            max-width: 640px;
            width: 100%;
            position: relative;
            overflow: hidden;
        }
        
        .content-box::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
        }
        
        #countdown {
            display: block;
            font-size: 1.125rem;
            font-weight: 500;
            color: #205848;
            padding: 1.5rem;
            background-color: rgba(32, 88, 72, 0.05);
            border-radius: 0.75rem;
            border: 1px solid rgba(32, 88, 72, 0.1);
            position: relative;
            overflow: hidden;
            animation: fadeInUp 0.6s ease-out 0.2s both;
        }
        
        .progress-container {
            width: 100%;
            height: 6px;
            background-color: rgba(32, 88, 72, 0.1);
            border-radius: 3px;
            margin-top: 1rem;
            overflow: hidden;
            position: relative;
        }
        
        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #205848, #2d7a5f);
            border-radius: 3px;
            transition: width 1s linear;
            position: relative;
            overflow: hidden;
        }
        
        .progress-bar::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            animation: shimmer 2s infinite;
        }
        
        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        /* Header Section Styles */
        .header-section {
            text-align: center;
            margin-bottom: 2rem;
        }

        .verification-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background: linear-gradient(135deg, #205848, #1a4a3e);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 2rem;
            font-size: 0.875rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            box-shadow: 0 4px 12px rgba(32, 88, 72, 0.3);
            animation: fadeInUp 0.6s ease-out;
        }

        .verification-icon {
            width: 1.25rem;
            height: 1.25rem;
        }

        .main-title {
            font-size: 1.875rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 2rem;
            line-height: 1.2;
            animation: fadeInUp 0.6s ease-out 0.1s both;
        }

        .verification-number {
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            border: 2px solid #e5e7eb;
            border-radius: 0.75rem;
            padding: 1.5rem;
            margin-bottom: 2rem;
            animation: fadeInUp 0.6s ease-out 0.2s both;
        }

        .number-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 600;
            color: #6b7280;
            margin-bottom: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .number-display {
            font-size: 2.25rem;
            font-weight: 800;
            color: #1f2937;
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            letter-spacing: 0.1em;
            background: linear-gradient(135deg, #205848, #1a4a3e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* Support Section Styles */
        .support-section {
            margin: 2rem 0;
        }

        .support-card-modern {
            background: linear-gradient(135deg, #ffffff, #f8fafc);
            border: 1px solid #e2e8f0;
            border-radius: 0.75rem;
            overflow: hidden;
            transition: all 0.4s ease;
            animation: fadeInUp 0.6s ease-out 0.3s both;
        }



        .support-header {
            background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
            padding: 1.5rem 1.5rem 1rem 1.5rem;
            border-bottom: 1px solid #e2e8f0;
        }

        .support-badge {
            display: inline-block;
            margin-bottom: 0.75rem;
        }

        .support-badge-text {
            background: linear-gradient(135deg, #205848, #1a4a3e);
            color: white;
            padding: 0.375rem 0.875rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .support-title-modern {
            font-size: 1.25rem;
            font-weight: 700;
            color: #1e293b;
            margin: 0;
            line-height: 1.3;
        }

        .support-body {
            padding: 1.5rem;
        }

        .support-description {
            font-size: 0.9rem;
            color: #64748b;
            margin: 0 0 1.5rem 0;
            line-height: 1.6;
        }

        .contact-methods {
            margin-bottom: 1.5rem;
        }

        .contact-item {
            background: linear-gradient(135deg, #f8fafc, #f1f5f9);
            border: 1px solid #e2e8f0;
            border-radius: 0.75rem;
            padding: 1rem;
            transition: all 0.3s ease;
        }

        .contact-item:hover {
            background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
            border-color: #cbd5e1;
            transform: translateY(-1px);
        }

        .contact-label {
            font-size: 0.75rem;
            font-weight: 600;
            color: #64748b;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-bottom: 0.5rem;
        }

        .contact-link {
            color: #205848;
            text-decoration: none;
            font-weight: 600;
            font-size: 0.9rem;
            display: inline-block;
            padding: 0.25rem 0;
            border-bottom: 2px solid transparent;
            transition: all 0.2s ease;
        }

        .contact-link:hover {
            color: #1a4a3e;
            border-bottom-color: #205848;
        }

        .support-footer {
            border-top: 1px solid #e2e8f0;
            padding-top: 1rem;
        }

        .response-time {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .response-indicator {
            width: 0.5rem;
            height: 0.5rem;
            background: linear-gradient(135deg, #205848, #1a4a3e);
            border-radius: 50%;
            animation: pulse-indicator 2s infinite;
        }

        .response-text {
            font-size: 0.8rem;
            color: #64748b;
            font-weight: 500;
        }

        @keyframes pulse-indicator {
            0%, 100% {
                opacity: 1;
                transform: scale(1);
            }
            50% {
                opacity: 0.7;
                transform: scale(1.1);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* RTL Support */
        [dir="rtl"] .support-card {
            direction: rtl;
        }

        [dir="rtl"] .verification-badge {
            direction: rtl;
        }

        [dir="rtl"] .support-email:hover {
            transform: translateX(-2px);
        }
        
        .countdown-text {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            font-weight: 600;
        }
        
        .countdown-number {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 2.5rem;
            height: 2rem;
            background-color: #205848;
            color: white;
            border-radius: 0.375rem;
            font-weight: 700;
            font-size: 1rem;
            box-shadow: 0 2px 4px rgba(32, 88, 72, 0.2);
        }
        
        @media (max-width: 640px) {
            .suspended-page {
                padding: 1rem;
            }
            
            .content-box {
                min-width: auto;
                padding: 2rem 1.5rem;
                margin: 0 1rem;
            }
            
            .suspended-page h1 {
                font-size: 1.5rem;
            }
            
            .lang-btn {
                top: 1rem;
                {{ session('locale') == 'ar' ? 'left' : 'right' }}: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="suspended-page">
        <!-- Language Switcher Button -->
        <a href="{{ route('switch.language', ['locale' => session('locale') == 'en' ? 'ar' : 'en']) }}" class="lang-btn">
            {{ session('locale') == 'ar' ? 'En' : 'ar' }}
        </a>
        
        <div class="content-box">
            <div class="header-section">
                <div class="verification-number">
                    <span class="number-label">{{ __('Verification Code') }}</span>
                    <div class="number-display">
                        {{ $responseData['random'] }}
                    </div>
                </div>
            </div>
            
            <div class="support-section">
                <div class="support-card-modern">
                    <div class="support-header">
                        <div class="support-badge">
                            <span class="support-badge-text">{{ __('Support') }}</span>
                        </div>
                        <h3 class="support-title-modern">{{ __('Need Help?') }}</h3>
                    </div>
                    
                    <div class="support-body">
                        <p class="support-description">
                            {{ __('If you face any issue, please contact our support team for assistance') }}
                        </p>
                        
                        <div class="contact-methods">
                            <div class="contact-item">
                                <div class="contact-label">{{ __('Email Support') }}</div>
                                <a href="mailto:<EMAIL>" class="contact-link">
                                    <EMAIL>
                                </a>
                            </div>
                        </div>
                        
                        <div class="support-footer">
                            <div class="response-time">
                                <span class="response-indicator"></span>
                                <span class="response-text">{{ __('We typically respond within 24 hours') }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div id="countdown" style="{{ $showCountdown ? '' : 'display: none;' }}">
                <div class="countdown-text">
                    {{ __('Resend in') }} 
                    <span id="countdown_span" class="countdown-number">{{ $remainingSeconds }}</span> 
                    {{ __('seconds') }}
                </div>
                <div class="progress-container">
                    <div id="progress-bar" class="progress-bar" style="width: {{ $showCountdown ? ($remainingSeconds / 180 * 100) : 0 }}%;"></div>
                </div>
            </div>
            <a href="{{ route('nafath.resend' , $responseData['national_id']) }}" id="resend-btn" class="btn" style="{{ $showResendButton ? '' : 'pointer-events: none; opacity: 0.6; display: none;' }}" {{ $showResendButton ? '' : 'disabled' }}>
                {{ __('Resend') }}
            </a>
        </div>
    </div>

    <script>
        document.addEventListener("DOMContentLoaded", function () {
            let countdown = {{ $remainingSeconds }};
            const totalTime = 180;
            const showCountdown = {{ $showCountdown ? 'true' : 'false' }};
            const showResendButton = {{ $showResendButton ? 'true' : 'false' }};
            const resendBtn = document.getElementById('resend-btn');
            const countdownSpan = document.getElementById('countdown_span');
            const progressBar = document.getElementById('progress-bar');
            
            // Only run countdown if we should show it and countdown > 0
            if (showCountdown && countdown > 0) {
                // Initialize countdown display
                countdownSpan.textContent = countdown;
                
                const timer = setInterval(() => {
                    countdown--;
                    countdownSpan.textContent = countdown;
                    
                    // Update progress bar
                    const progressPercentage = (countdown / totalTime) * 100;
                    progressBar.style.width = progressPercentage + '%';
                    
                    // Add pulse effect when time is running low
                    if (countdown <= 30) {
                        countdownSpan.style.animation = 'pulse 1s infinite';
                        progressBar.style.background = 'linear-gradient(90deg, #dc2626, #ef4444)';
                    } else if (countdown <= 60) {
                        progressBar.style.background = 'linear-gradient(90deg, #f59e0b, #fbbf24)';
                    }
        
                    if (countdown <= 0) {
                        clearInterval(timer);
                        resendBtn.removeAttribute('disabled');
                        resendBtn.innerHTML = "{{ __('Resend') }}";
                        resendBtn.style.cursor = 'pointer';
                        resendBtn.style.opacity = '1';
                        resendBtn.style.pointerEvents = 'auto';
                        resendBtn.style.display = 'block';
                        
                        // Hide countdown when finished
                         document.getElementById('countdown').style.display = 'none';
                    }
                }, 1000);
            }
    
            // resendBtn.addEventListener('click', function(e) {
            //     if (this.hasAttribute('disabled')) {
            //         e.preventDefault();
            //     }
            // });
    
            // Begin polling for verification
            // const responseData = @json($responseData ?? []);
            // console.log(responseData);
            
    const responseData = @json($responseData);
        console.log(responseData);


            const pollInterval = setInterval(() => {
                fetch("{{ route('nafath.verify.status') }}", {
    method: "POST",
    headers: {
        "Content-Type": "application/json",
        "Accept": "application/json",
        "X-CSRF-TOKEN": "{{ csrf_token() }}"
    },
    body: JSON.stringify({
        transId: responseData.transId,
        random: responseData.random,
        national_id: responseData.national_id
    })
})
    .then(async (response) => {
        const reader = response.body.getReader();
        const decoder = new TextDecoder("utf-8");
        let result = '';
        
        // Keep reading chunks until done
        while (true) {
            const { done, value } = await reader.read();
            if (done) break;
            result += decoder.decode(value, { stream: true });
        }

        // Final decode
        result += decoder.decode(); // flush decoder
        console.log("Raw stream result:", result);

        // Try parsing JSON
        let data;
        try {
            data = JSON.parse(result);
        } catch (e) {
            console.error("Failed to parse JSON:", e);
            return;
        }

        console.log("Parsed data:", data);
        console.log("status data:", data.status);

        if (data.status === 'COMPLETED') {
            clearInterval(pollInterval);
            window.location.href = "{{ url('admin/register') }}" + "?nafath=" + encodeURIComponent(JSON.stringify({ national_id: responseData.national_id }));
        }
    })
    .catch(error => {
        console.error("Polling error:", error);
    });
            }, 10000);
        });
    </script>
</body>
</html>