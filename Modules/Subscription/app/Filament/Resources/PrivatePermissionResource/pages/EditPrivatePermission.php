<?php

namespace Modules\Subscription\app\Filament\Resources\PrivatePermissionResource\Pages;

use Modules\Account\app\Filament\Resources\AccountsResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Modules\Subscription\app\Filament\Resources\PrivatePermissionResource;

class EditPrivatePermission extends EditRecord
{
    protected static string $resource = PrivatePermissionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
