<?php

namespace Modules\Subscription\app\Filament\Resources\FeatureResource\Pages;

use Modules\Account\app\Filament\Resources\AccountsResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Modules\Subscription\app\Filament\Resources\FeatureResource;
use Modules\Subscription\app\Filament\Resources\PrivatePermissionResource;

class CreateFeature extends CreateRecord
{
    protected static string $resource = FeatureResource::class;
}
