<?php

namespace Modules\Subscription\app\Filament\Resources\FeatureResource\Pages;

use Modules\Account\app\Filament\Resources\AccountsResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Modules\Subscription\app\Filament\Resources\FeatureResource;
use Modules\Subscription\app\Filament\Resources\PrivatePermissionResource;

class EditFeature extends EditRecord
{
    protected static string $resource = FeatureResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
