<?php

namespace Modules\Subscription\app\models;

use Illuminate\Database\Eloquent\Relations\HasMany;
use <PERSON>haleds\Shared\Models\BaseModel;

class Interval extends BaseModel
{
    /**
     * The "type" of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'integer';

    /**
     * @var array
     */
    protected $fillable = ['name', 'key', 'description', 'is_active', 'deleted_at', 'created_at', 'updated_at'];

    /**
     * @return HasMany
     */
    public function plans()
    {
        return $this->hasMany(plan::class);
    }

    /**
     * @return HasMany
     */
    public function subscriptions()
    {
        return $this->hasMany(Subscription::class);
    }
}
