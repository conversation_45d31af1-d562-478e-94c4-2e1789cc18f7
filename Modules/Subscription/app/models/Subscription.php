<?php

namespace Modules\Subscription\app\models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Khaleds\Shared\Models\BaseModel;
use Modules\Payment\app\Models\KhaledsPayment;

class Subscription extends BaseModel
{
    /**
     * The "type" of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'integer';

    /**
     * @var array
     */
    protected $fillable = [
        'interval_id',
        'plan_id',
        'subscription_id',
        'subscriber_type',
        'subscriber_id',
        'price',
        'count',
        'total_price',
        'start_at',
        'expired_at',
        'canceled_at',
        'status',
        'is_current',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'total_price' => 'decimal:2',
        'start_at' => 'datetime',
        'expired_at' => 'datetime',
        'canceled_at' => 'datetime',
        'is_current' => 'boolean',
    ];
    /**
     * @return BelongsTo
     */
    public function interval()
    {
        return $this->belongsTo(Interval::class);
    }

    /**
     * @return BelongsTo
     */
    public function plan()
    {
        return $this->belongsTo(Plan::class);
    }

    /**
     * @return BelongsTo
     */
    public function subscription()
    {
        return $this->belongsTo(Subscription::class);
    }

    /**
     * @return HasMany
     */
    public function subscriptionUsages()
    {
        return $this->hasMany(SubscriptionUsage::class);
    }

    public function subscriber(){
        return $this->morphTo();
    }

    public function order()
    {
        return $this->morphMany(KhaledsPayment::class,'order');
    }
}
