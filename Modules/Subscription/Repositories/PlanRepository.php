<?php

namespace Modules\Subscription\Repositories;

use Illuminate\Pagination\LengthAwarePaginator;
use Khaleds\Shared\Infrastructure\Repositories\Eloquent\RepositoriesAbstract;
use Modules\Subscription\app\models\Plan;
use Spatie\QueryBuilder\QueryBuilder;

class PlanRepository extends RepositoriesAbstract
{

    public function __construct(Plan $model)
    {
        parent::__construct($model);
    }


    public function getAllBy(array $condition = [], array $with = [], array $select = ['*']): LengthAwarePaginator
    {
        return QueryBuilder::for($this->model::class)
            ->select($select)
            ->where($condition)
            ->orderBy('order','asc')
            ->allowedFilters($this->model->getAllowedFilters())
            ->paginate(request()->limit ?? 10)
            ->appends(request()->query());
    }


}
