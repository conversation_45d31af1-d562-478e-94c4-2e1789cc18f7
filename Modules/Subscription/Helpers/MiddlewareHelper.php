<?php

namespace Modules\Subscription\Helpers;

use Filament\Notifications\Actions\Action; // Make sure to use this Action class
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Str;
use Modules\Subscription\app\Filament\Resources\PlanResource;
use Modules\Subscription\app\Filament\Resources\PlanResource\pages\SubscriptionPlans;
use Modules\Subscription\app\models\PrivatePermission;

class MiddlewareHelper
{
//todo make it singleton or move all as a trait

    public static function getShieldPermissionName($route)
    {
        try {

            // Handle resource routes with admin panel prefix
            if (str_contains($route, '.resources.')) {
                $parts = explode('.', $route);
                $resourceIndex = array_search('resources', $parts) + 1;
                // Get plural form of resource name (accounts, invoices, etc)
                $resource = Str::singular($parts[$resourceIndex]);
                $resource = str_replace('-', '::', $resource);
                $action = $parts[$resourceIndex + 1] ?? '';

                return match ($action) {
                    'index' => 'view_any_' . $resource,
                    'create' => 'create_' . $resource,
                    'edit' => 'update_' . $resource,
                    'view' => 'view_' . $resource,
                    'delete' => 'delete_' . $resource,
                    'bulk-delete' => 'delete_any_' . $resource,
                    'force-delete' => 'force_delete_' . $resource,
                    'bulk-force-delete' => 'force_delete_any_' . $resource,
                    'restore' => 'restore_' . $resource,
                    'bulk-restore' => 'restore_any_' . $resource,
                    default => null
                };
            }

            // Handle page routes
            if (str_contains($route, '.pages.')) {
                $parts = explode('.', $route);
                $page = end($parts);
                return 'page_' . Str::snake($page);
            }

            return null;
        } catch (\Exception $e) {
            return null;
        }


    }

    public static function checkPermission($request,$next,$route)
    {
        // fetch if this route exist in private permissions
        $privatePermissions = PrivatePermission::whereHas("permission", function ($query) use ($route) {
            $query->where('name', $route);
        })->first();

//        dd($route);
        //if in private permissions and user authed
        if (Auth::user() && !empty($privatePermissions)) {

            $user = Auth::user();

            //check if admin return
            if ($user->company)
                $user = $user->company->user;
            $activeSubscription = $user->activeSubscription;
            $usage = $activeSubscription->subscriptionUsages()->whereHas('feature', function ($query) use ($privatePermissions) {
                $query->where("private_permission_id", $privatePermissions->id);
            })->first();

            if (!empty($usage) && $usage->value > $usage->used) {

                //check for create as a post method
                if (Str::contains($route,'create_') && $request->method() == "GET")
                    return $next($request);

                $usage->used += 1;
                $usage->save();
                return $next($request);
            }

            //if authed user can't access this route
            if (empty($usage) && !$user->can($route))
            {
                return $next($request);
            }

            Notification::make()
                ->title(__('Access Denied'))
                ->body(__('You do not have access to this feature. Please upgrade your subscription.'))
                ->danger()
                ->persistent()
                ->actions([
                    Action::make('upgrade')
                        ->label(__('Upgrade Subscription'))
                        ->url(fn (): string => url('/admin/subscription-plans'))
                        ->button()
                        ->color('success'),

                    Action::make('back')
                        ->label(__('Go Back'))
                        ->url(url()->previous())
                        ->color('gray')
                        ->button(),
                ])
                ->send();

            if (URL::previous() == URL::current())
            return redirect('/admin');

            return redirect()->back();
        }
        return $next($request);
    }
}
