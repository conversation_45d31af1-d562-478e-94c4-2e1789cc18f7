<?php

namespace Modules\Subscription\Services;


use Khaleds\Shared\Services\ServiceAbstract;
use Modules\Subscription\Repositories\PlanRepository;

class PlanService extends ServiceAbstract
{

    public function __construct(PlanRepository $repository)
    {
        parent::__construct($repository);
    }


    function getDefaultPlan()
    {
        return $this->repository->getFirstBy(["is_default"=>1]);
    }

}


