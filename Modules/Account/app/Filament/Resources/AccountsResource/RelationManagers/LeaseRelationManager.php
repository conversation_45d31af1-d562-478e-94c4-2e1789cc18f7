<?php

namespace Modules\Account\app\Filament\Resources\AccountsResource\RelationManagers;

use Filament\Forms;
use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Modules\Lease\Enums\LeaseEnum;
use Filament\Tables\Columns\IconColumn;
use Illuminate\Database\Eloquent\Model;
use Modules\Property\Enums\PropertyStatus;
use Filament\Resources\RelationManagers\RelationManager;
use Illuminate\Support\HtmlString;

class LeaseRelationManager extends RelationManager
{
    protected static string $relationship = 'leases';
    public static function getTitle(Model $ownerRecord, string $pageClass): string
    {
        return __('Leases');
    }

    protected static function getPluralRecordLabel(): ?string
    {
        return __('Leases');
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label(__('id'))
                    ->sortable(),
                Tables\Columns\TextColumn::make('property.name')
                    ->label(__('Property'))
                    ->sortable(),
                Tables\Columns\TextColumn::make('tenant.member.first_name')
                    ->label(__('Tenant'))
                    ->sortable(),
                Tables\Columns\TextColumn::make('leaseUnits.unit.number')
                    ->label(__('Units'))
                    ->sortable(),
                Tables\Columns\TextColumn::make('start_date')
                    ->label(__('Start Date'))
                    ->dateTime('d-m-Y')  // Use 'd-m-Y' to display the date in dd-mm-yyyy format
                    ->sortable(),

                Tables\Columns\TextColumn::make('end_date')
                    ->label(__('End Date'))
                    ->dateTime('d-m-Y')  // Use 'd-m-Y' to display the date in dd-mm-yyyy format
                    ->sortable(),
                Tables\Columns\TextColumn::make('status')
                    ->label(__('Status'))
                    ->badge()
                    ->formatStateUsing(fn (string $state) => LeaseEnum::getLabel($state))
                    ->color(fn (string $state): string => LeaseEnum::getColor($state))
                    ->sortable(),
                Tables\Columns\TextColumn::make('rent_amount')
                    ->label(__('Rent Amount'))
                    ->formatStateUsing(function ($state) {
                        return new HtmlString(
                            '<div class="">' .
                            number_format($state) .
                            ' <span class="icon-saudi_riyal"></span>' .
                            '</div>'
                        );
                    })
                    ->sortable(),
                IconColumn::make('EJAR_registration_status')
                    ->label(__('EJAR Registration Status'))
                    ->boolean()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('Created at'))
                    ->since()
                    ->tooltip(fn($record) => $record->created_at->format('Y-m-d H:i:s')) // Display full date and time on hover
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('Updated at'))
                    ->since()
                    ->tooltip(fn($record) => $record->updated_at->format('Y-m-d H:i:s')) // Display full date and time on hover
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
            ])
            ->filters([
                //
            ])
            ->headerActions([
                // Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                // Tables\Actions\EditAction::make(),
                // Tables\Actions\DeleteAction::make(),
                Tables\Actions\ViewAction::make()
                    ->url(fn ($record) => route('filament.admin.resources.leases.view', ['record' => $record]))
                    ->openUrlInNewTab()
            ])
            ->bulkActions([
                // Tables\Actions\BulkActionGroup::make([
                //     Tables\Actions\DeleteBulkAction::make(),
                // ]),
            ]);
    }
}
