<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    @foreach ($members as $member)
        <div class="fi-in-repeatable-item block rounded-xl bg-white py-4 px-4 shadow-sm ring-1 ring-gray-950/5 dark:bg-white/5 dark:ring-white/10 relative">
            @if($member->member_role == \Modules\Lease\Enums\LeaseMemberTypesEnum::LESSOR_REPRESENTER)
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="21" viewBox="0 0 20 21" fill="none" class="absolute ltr:right-2 rtl:left-2 top-2">
                    <path d="M16.686 1.02835V0.199951H3.42997V1.02835C3.42997 2.39515 2.31157 3.51395 0.944367 3.51395H0.115967V4.47515C0.11532 7.59244 0.954335 10.6524 2.54485 13.3334C4.13536 16.0144 6.41864 18.2175 9.15477 19.7112L10.058 20.2L10.9612 19.7112C13.6973 18.2175 15.9807 16.0144 17.5712 13.3334C19.1617 10.6524 20.0007 7.59246 20 4.47515V3.51395H19.1716C17.8044 3.51395 16.686 2.39555 16.686 1.02835ZM8.81517 12.9672L5.74157 9.90155L6.91797 8.72515L8.81517 10.6308L13.198 6.23955L14.3744 7.41595L8.81517 12.9672Z" fill="#C69D67"/>
                </svg>
            @endif
            <div class="flex flex-col gap-2">
                <div class="flex items-center gap-2 flex-wrap">
                    <span class="text-[#31846C] dark:text-primary-400 text-base font-semibold">
                        {{ $member->member->name ?? '' }}
                        @if ($member->percentage != 0)
                            <span class="text-base text-[#C69D67] font-black plaintext">
                                {{ number_format($member->percentage) }}%
                            </span>
                        @endif
                    </span>

                </div>
                @if(!$member->is_organization)
                <div class="flex items-center gap-2 flex-wrap justify-between">
                    <span class="text-[#112C24] dark:text-white text-sm">
                        {{ __('Phone No') }}.:
                        <span class="text-[#6F6F6F] dark:text-gray-400">
                            {{ $member->member->phone ?? '' }}
                        </span>
                    </span>
                    <span class="text-[#112C24] dark:text-white text-sm">
                        {{ __('National ID') }}:
                        <span class="text-[#6F6F6F] dark:text-gray-400">
                            {{ $member->member->national_id ?? '' }}
                        </span>
                    </span>
                </div>
                @else
                <div class="flex items-center gap-2 flex-wrap justify-between">
                    <span class="text-[#112C24] dark:text-white text-sm">
                        {{ __('Unified Number') }}:
                        <span class="text-[#6F6F6F] dark:text-gray-400">
                            {{ $member->member->unified_number ?? '' }}
                        </span>
                    </span>
                    <span class="text-[#112C24] dark:text-white text-sm">
                        {{ __('Ownership Document Number') }}:
                        <span class="text-[#6F6F6F] dark:text-gray-400">
                            {{ $member->member->ownership_document_number ?? '' }}
                        </span>
                    </span>
                </div>
                @endif

                {{-- Check if this lease has a bank account and if this member owns that bank account --}}
                @if(isset($record) && $record->bankAccount && $record->bankAccount->bankable_type == get_class($member->member) && $record->bankAccount->bankable_id == $member->member->id)
                <div>
                    <span class="text-[#112C24] dark:text-white text-sm">
                        {{ __('IBAN') }}:
                        <span class="text-[#6F6F6F] dark:text-gray-400">
                            {{ $record->bankAccount->iban ?? '' }}
                        </span>
                    </span>
                </div>
                @endif
            </div>
        </div>
    @endforeach
</div>
