<?php

namespace Modules\Lease\Services;

use Modules\Account\app\Models\Account;
use Modules\Lease\Enums\LeaseEnum;
use Modules\Lease\Enums\LeasePaymentEnum;
use Modules\Lease\Enums\LeaseTypesEnum;
use Modules\Organization\app\Models\Organization;
use Modules\Payment\Enums\PaymentMethodTypeEnum;

class LeasePaymentService
{
    public $total_previews_amount = 0;

    //total lease balance
    public function getTotalLeaseBalance(
        string $start_date,
        string $end_date,
        string $payment_repeat,
        string|null $payment_repeat_type,
        float $total_rent_amount,
        array $services_list,
        array $commercialLeaseServices,
        string $leaseType,
        array $owners
    )
    {
        $hasValidVAT = $this->checkValidVatForLeaseMembers($owners);
        $start_date = $this->convertDateFormat($start_date);
        $end_date = $this->convertDateFormat($end_date);
        $interval = $this->getInterval($payment_repeat, $payment_repeat_type);
        $numberOfMonths = $this->getNumberOfAllmonths($start_date, $end_date);
        $payment_repeat == LeasePaymentEnum::ONCE ? $numberOfIntervals = 1 : $numberOfIntervals = $this->getNumberOfIntervals($start_date, $interval, $end_date);
        $total_lease_rent_amount = $this->getTotalAmount($total_rent_amount, $numberOfMonths);
        $total_lease_services_amount = $this->getTotalCommercialLeaseServicesAmount($commercialLeaseServices,$services_list, $numberOfMonths, $numberOfIntervals , $leaseType);

        return [
            'total_lease_rent_amount' => $total_lease_rent_amount,
            'total_lease_services_amount' => $total_lease_services_amount['all_services_amount'],
            'total_lease_balance' => round($total_lease_rent_amount + $total_lease_services_amount['all_services_amount'], 2),
            'units_services' => $total_lease_services_amount['units_services']
        ];
    }


    public function generatePaymentSchedule(
        $start_date,
        $end_date,
        $total_rent_amount, //total rent amount
        $total_service_amount, //total services amount
        $paid_amount, //total paid amount
        $payment_repeat, //once , repeated
        $payment_repeat_type, //[monthly, annually, ...]
        $days_permitted = 0,
        $leaseType,
        $owners,
    )
    {
        $hasValidVAT = $this->checkValidVatForLeaseMembers($owners);
        $interval = $this->getInterval($payment_repeat, $payment_repeat_type);
        $start_date = $this->convertDateFormat($start_date);
        $end_date = $this->convertDateFormat($end_date);
        $startSchedule = $this->getScheduleDate($start_date, $interval);
        $numberOfMonths = $this->getNumberOfAllMonths($start_date, $end_date);
        $total_lease_rent_amount = round($total_rent_amount, 2);
        $total_lease_service_amount = round($total_service_amount, 2);
        $totalAmountScheduled = 0;
        $totalServiceAmountScheduled = 0;
        $bill_number = 1;

        if ($interval != null) {
            $numberOfIntervals = $this->getNumberOfIntervals($start_date, $interval, $end_date);
            $installmentAmount = $total_lease_rent_amount / $numberOfIntervals;
            $installmentServiceAmount = $total_lease_service_amount / $numberOfIntervals;
            $schedule = [];
            $periodSchedule = new \DatePeriod($startSchedule, $interval, $end_date);
            foreach ($periodSchedule as $dt) {
                $installment = new \stdClass();
                $installment->installment_date = $dt->format("Y-m-d");
                $installment->due_date = (clone $dt)->modify("+$days_permitted days")->format("Y-m-d"); // Calculate due date
                $installment->installment_amount = round($installmentAmount);
                $installment->installment_service_amount = round($installmentServiceAmount);
                $installment->total_amount = round($installmentAmount) + round($installmentServiceAmount);
                $installment->vat = 0;
                if($leaseType == LeaseTypesEnum::COMMERCIAL && $hasValidVAT)
                {
                    $installment->vat = $installment->installment_amount * config('lease.vat');
                    $installment->total_amount += $installment->vat;
                }
                $totalAmountScheduled += $installmentAmount;
                $totalServiceAmountScheduled += $installmentServiceAmount;
                $installment->payment = '';
                $installment->downloadUrl = '';
                $installment->bill_number = $bill_number;
                $installment->status = 'due';
                if($installment->due_date <= date('Y-m-d')){
                    $installment->status = 'overdue';
                    $installment->previous = true;
                    $this->total_previews_amount += $installment->total_amount;
                }
                $bill_number++;
                $schedule[] = $installment;
            }
        } else{
            //in case once payment
            $installment = new \stdClass();
            $installment->installment_date = $startSchedule->format("Y-m-d");
            $installment->due_date = $startSchedule->modify("+$days_permitted days")->format("Y-m-d"); // Calculate due date
            $installment->installment_amount = floor($total_lease_rent_amount);
            $installment->installment_service_amount = floor($total_lease_service_amount);
            $installment->paid_amount = floor($paid_amount);
            $installment->total_amount = floor($installment->installment_amount + $installment->installment_service_amount);
            $installment->vat = 0;
            if($leaseType == LeaseTypesEnum::COMMERCIAL && $hasValidVAT)
            {
                $installment->vat = $installment->installment_amount * config('lease.vat');
                $installment->total_amount += $installment->vat;
            }
            $installment->bill_number = $bill_number;
            $totalAmountScheduled += $total_lease_rent_amount - $paid_amount;
            $totalServiceAmountScheduled += $total_lease_service_amount;
            $installment->payment = '';
            $installment->downloadUrl = '';
            $installment->status = 'due';
            if($installment->due_date <= date('Y-m-d')){
                $installment->status = 'overdue';
                $installment->previous = true;
                $this->total_previews_amount += $installment->total_amount;
            }

            $schedule[] = $installment;
        }

        $previous = $this->getPreviousSchedule($start_date,$total_lease_rent_amount,$totalAmountScheduled,$totalServiceAmountScheduled,$paid_amount,$total_lease_service_amount,$days_permitted , $leaseType, $payment_repeat , $hasValidVAT);
        if($previous){
            array_unshift($schedule, $previous);
        } else {
            if ($paid_amount > 0 && $payment_repeat != LeasePaymentEnum::ONCE) {
                //set paid amount to first installment
                $schedule[0]->paid_amount = $paid_amount;
                $schedule[0]->total_amount -= $paid_amount;
            }
        }
        //add count and serial keys into each object
        $loopIndex = 1 ;
        $count = count($schedule);
        $calculatedRent  = 0;
        $calculatedService  = 0;
        
        foreach ($schedule as $element){
            $calculatedRent += $element->installment_amount;
            $calculatedService += $element->installment_service_amount;
            $element->serial = $loopIndex;
            $element->count = $count;
            $loopIndex++;
        }

        $remaingRent    = $total_rent_amount - $calculatedRent ;
        $remaingService = $total_lease_service_amount -$calculatedService ;
        
        if($calculatedRent >0 || $calculatedService >0)
        {
            foreach ($schedule as $k => $element){
                if($k ==$count -1)
                {
                    $element->installment_amount += $remaingRent;
                    $element->installment_service_amount += $remaingService;
                    $element->total_amount += ($remaingRent + $remaingService);
                    if($leaseType == LeaseTypesEnum::COMMERCIAL && $hasValidVAT)
                    {
                        $vat = $element->installment_amount * config('lease.vat');
                        $element->vat = $vat;
                        $element->total_amount = $vat + $element->installment_service_amount + $element->installment_amount;
                    }
                }
            }
        }

        return $schedule;
    }

    private function getInterval($payment_repeat, $payment_repeat_type)
    {
        if (strtolower($payment_repeat) == LeasePaymentEnum::ONCE) {
            return null; // No interval needed for a one-time payment
        } elseif (strtolower($payment_repeat) == LeasePaymentEnum::REPEATED) {
            switch (strtolower($payment_repeat_type)) {
                case LeasePaymentEnum::MONTHLY:
                    return new \DateInterval('P1M');
                case LeasePaymentEnum::QUARTERLY:
                    return new \DateInterval('P3M');
                case LeasePaymentEnum::HALF_ANNUALLY:
                    return new \DateInterval('P6M');
                case LeasePaymentEnum::ANNUALLY:
                    return new \DateInterval('P1Y');
                default:
                    throw new \Exception("Unknown payment type: $payment_repeat_type");
            }
        }
    }

    public function convertDateFormat($date,$format = 'd/m/Y')
    {
        $dateObject = \DateTime::createFromFormat($format, $date);

        if ($dateObject && $dateObject->format($format) === $date) {
            $isoFormatDate = $dateObject->format('Y-m-d');
            return new \DateTime($isoFormatDate);
        }
        return new \DateTime($date);
    }

    public function getPreviousSchedule(
        $start_date,
        $totalAmount,
        $totalAmountScheduled,
        $totalServiceAmountScheduled,
        $paid_amount,
        $totalServiceAmount,
        $days_permitted,
        $leaseType,
        $payment_repeat,
        $hasValidVAT
    )
    {

        $totalPreviousAmount = round(floatval($totalAmount) - floatval($totalAmountScheduled));
        $totalPreviousServiceAmount = round(floatval($totalServiceAmount) - floatval($totalServiceAmountScheduled));
        $total = $totalPreviousAmount + $totalPreviousServiceAmount - $paid_amount;
        
        $status = 'overdue';
        if ($payment_repeat != LeasePaymentEnum::ONCE) { //if not future pay or previous pay for once type
            $result = [
                'installment_date' => $start_date->format("Y-m-d"),
                'due_date' => $start_date->modify("+$days_permitted days")->format("Y-m-d"),
                'installment_amount' => round($totalPreviousAmount),
                'installment_service_amount' => round($totalPreviousServiceAmount),
                'paid_amount' => floatval($paid_amount),
                'vat' => ($leaseType == LeaseTypesEnum::COMMERCIAL && $hasValidVAT) ? $totalPreviousAmount * config('lease.vat') : 0,
                'total_amount' => ($leaseType == LeaseTypesEnum::COMMERCIAL && $hasValidVAT) ? $total + ($totalPreviousAmount * config('lease.vat')) : $total,
                'previous' => true,
                'status' => $status,
                'bill_number' => 0,
            ];
            if($total > 0) { //if the previous is partially paid
                $this->total_previews_amount += $result['total_amount'];
                return (object) $result;
            } elseif ($paid_amount > 0 && $start_date <  new \DateTime()) { //paid amount in previous due date even the total is zero
                $this->total_previews_amount += $result['total_amount'];
                return (object) $result;
            }
        }
        return null;
    }

    public function getNumberOfIntervals($start_date, $interval, $end_date)
    {
        $period = new \DatePeriod($start_date, $interval, $end_date);

        return iterator_count($period); // Including the end month
    }

    public function getNumberOfAllmonths($start_date, $end_date)
    {
        $interval = new \DateInterval('P1M');
        $period = new \DatePeriod($start_date, $interval, $end_date);

        return iterator_count($period);
    }
    public function getTotalAmount($total_rent_amount, $numberOfMonths)
    {
        if ($numberOfMonths > 12) {
            $amount_of_month = $total_rent_amount / 12;
            return round($amount_of_month * $numberOfMonths, 2);
        } else {
            return $total_rent_amount;
        }
    }
    public function getTotalLeaseServicesAmount(array $total_services_amount, int $numberOfMonths, int $numberOfIntervals)
    {
        $services_sum = 0.00;
        foreach ($total_services_amount as $unit_id => &$unit_services)
        {
            foreach ($unit_services as $type => &$services_details) {
                foreach ($services_details as $service_id => &$services_item) {
                    $services_sum += $services_item['lease_service_amount'];
                    $amount_to_be_paid_per_interval = $this->getTotalAmount($services_item['lease_service_amount'], $numberOfMonths) / $numberOfIntervals;
                    $services_item['to_be_paid_amount'] = round($amount_to_be_paid_per_interval ,2);
                }
            }
        }
        return [
            'all_services_amount' => $this->getTotalAmount($services_sum, $numberOfMonths),
            'units_services' => $total_services_amount,
        ];
    }

    public function getTotalCommercialLeaseServicesAmount(array $commercialLeaseServices , array $leaseUnitServices ,int $numberOfMonths, int $numberOfIntervals , $leaseType)
    {
        if($leaseType == LeaseTypesEnum::COMMERCIAL)
        {
            $data = $this->getTotalLeaseServicesAmount($leaseUnitServices , $numberOfMonths , $numberOfIntervals);
            $commercialSum = $this->getCommercialLeaseSum($commercialLeaseServices  , $numberOfMonths);
            $data['all_services_amount'] += $commercialSum;
            return $data;
        }else{
            return $this->getTotalLeaseServicesAmount($leaseUnitServices , $numberOfMonths , $numberOfIntervals);
        }
    }
    public function getTotalLeaseServicesAmountOld(array $total_services_amount, int $numberOfMonths)
    {
        $services_sum = 0.00;
        $units_list = [
            'unit_id_1' => [
                'services' => [
                    'service_id' => [
                        'lease_service_amount' => 1000,
                        'to_be_paid_amount' =>10 //null
                    ],
                    // ...
                ],
                'other_services' => [
                    'service_id' => [
                        'lease_service_amount' => 1000,
                        'to_be_paid_amount' => 10 //send with null
                    ],
                    // ...
                ]
            ],
            // ...
        ];
        foreach ($total_services_amount as $unit_services)
        {
            foreach ($unit_services as $key => $service_amount) {
                $services_sum += $service_amount;
            }
        }

        return $this->getTotalAmount($services_sum, $numberOfMonths);
    }

    public function getCommercialLeaseSum($commercialLeaseServices, $numberOfMonths)
    {
        // Calculate the sum of the `value` from the services
        $total = array_reduce($commercialLeaseServices, function ($carry, $item) {
            return $carry + (isset($item['value']) ? (float) $item['value'] : 0);
        }, 0);

        // Apply the logic based on the number of months
        if ($numberOfMonths > 12) {
            $total = ($total / 12) * $numberOfMonths;
        }

        return $total;
    }

    //to check paid amount is valid amount
    public function isPaidAmountValid(
        $start_date,
        $end_date,
        $paid_amount,
        $payment_repeat, //once , repeated
        $payment_repeat_type, //[monthly, annually, ...]
        $total_lease_balance
    )
    {
        $end_date = $this->convertDateFormat($end_date);
        if ($payment_repeat == LeasePaymentEnum::ONCE || $this->isHistorical($end_date)) { //once payment or historical lease
            $max_paid_amount = round($total_lease_balance);
        } else {
            $interval = $this->getInterval($payment_repeat, $payment_repeat_type);
            $start_date = $this->convertDateFormat($start_date);
            $start_schedule = $this->getScheduleDate($start_date, $interval);
            $numberOfAllMonths = $this->getNumberOfIntervals($start_date, $interval, $end_date);
            $installment_of_lease_balance = $total_lease_balance / $numberOfAllMonths;
            $numberOfPreviousScheduledMonths = $this->getNumberOfIntervals($start_date, $interval, $start_schedule); //previous period
            if ($numberOfPreviousScheduledMonths > 0) {
                $max_paid_amount = round($numberOfPreviousScheduledMonths * $installment_of_lease_balance);
            } else {
                $max_paid_amount = round($installment_of_lease_balance);
            }
        }
        return ['status'=>round($paid_amount) <= $max_paid_amount, 'max_paid_amount' => $max_paid_amount];
    }

    public function isHistorical(\DateTime $end_date)
    {
        return $end_date < new \DateTime();
    }

    function getStartScheduleDate($start_date, $payment_repeat, $payment_repeat_type)
    {
        if ($payment_repeat == LeasePaymentEnum::ONCE) {
            $start_date = $this->convertDateFormat($start_date);
            return $start_date->format('Y-m-d');
        }
        $interval = $this->getInterval($payment_repeat, $payment_repeat_type);
        $scheduleDate =  $this->getScheduleDate($this->convertDateFormat($start_date), $interval);
        return $scheduleDate->format('Y-m-d');
    }

    private function getScheduleDate(\DateTime $start_date, ?\DateInterval $interval = null): ?\DateTime
    {
        if ($interval === null) {
            return $start_date; //if once payment
        }
        $now = new \DateTime();
        $nextDate = clone $start_date;
        $originalDay = (int)$start_date->format('d');

        while ($nextDate <= $now) {
            $nextDate->add($interval);
            // Preserve the original day of month
            $nextDate->setDate(
                (int)$nextDate->format('Y'),
                (int)$nextDate->format('m'),
                $originalDay
            );
        }

        return $nextDate;
    }

    public function checkValidVatForLeaseMembers($owners): bool
    {
        $owners = collect($owners)->groupBy('member_type');

        $organizationIds = $owners->get('organization', collect())->pluck('member_id');
        $accountIds      = $owners->get('individual', collect())->pluck('member_id');

        return Organization::whereIn('id', $organizationIds)
                    ->whereNotNull('vat_number')
                    ->where('vat_number', '!=', 0)
                    ->exists()
            || Account::whereIn('id', $accountIds)
                    ->whereNotNull('vat_number')
                    ->where('vat_number', '!=', 0)
                    ->exists();
    }
}
