<?php

namespace Modules\MaintenanceRequest\app\Services;

use App\Models\User;
use Illuminate\Support\Facades\Crypt;
use Khaleds\Notifications\Models\NotificationsTemplate;
use Khaleds\Notifications\Services\SendNotification;
use Khaleds\Shared\Services\ServiceAbstract;

use Modules\Account\app\Models\Account;
use Modules\MaintenanceRequest\app\Repositories\MaintenanceRequestRepository;

class MaintenanceRequestService extends ServiceAbstract
{

    public function __construct(MaintenanceRequestRepository $repository)
    {
        parent::__construct($repository);
    }

    public function create(array $data)
    {
        $maintenance_request = $this->repository->create($data);
        if(array_key_exists('images',$data)) {
            foreach ($data['images'] as $image){
                $maintenance_request->addMedia($image)
                    ->toMediaCollection('maintenance_requests');
            }
        }
        $template = NotificationsTemplate::where(['key' => 'new_maintenance_req'])->first();
        $broker = $maintenance_request->lease?->broker ?? null;
        $service = $maintenance_request->service ?? null;
        if ($template && $broker) {
            SendNotification::make(["fcm-web"])
                ->template($template->key)
                ->model(User::class)
                ->id($broker->id)
                ->findBody(['{title}', '{expected_date}'])
                ->replaceBody([$service?->name, $maintenance_request->expected_date])
                ->icon($template->icon)
                ->url(url($template->url))
                ->privacy('private')
                ->database(true)
                ->fire();
        }
        return $maintenance_request;
    }

    public function filter(array $condition = [], array $with = [], array $select = ['*'])
    {
        return $this->repository->filterAllBy($condition, $with, $select);
    }
}


