<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Modules\MaintenanceRequest\Enums\maintenanceRequestStatusEnum;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('maintenance_requests', function (Blueprint $table) {
            // Check for invalid data before modifying
            $invalidRecords = DB::table('maintenance_requests')
                ->whereNotIn('status', maintenanceRequestStatusEnum::getMaintenanceRequestStatusValues())
                ->count();

            if ($invalidRecords > 0) {
                throw new \Exception('There are invalid status values in the leases table');
            }

            DB::statement("ALTER TABLE maintenance_requests MODIFY COLUMN status ENUM(
            '".maintenanceRequestStatusEnum::PENDING."',
            '".maintenanceRequestStatusEnum::OPEN."',
            '".maintenanceRequestStatusEnum::CLOSED."',
            '".maintenanceRequestStatusEnum::TERMINATED."')
            NOT NULL DEFAULT
            '".maintenanceRequestStatusEnum::PENDING."'
            ");
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('maintenance_requests', function (Blueprint $table) {
            $table->dropColumn('status');
        });
    }
};
