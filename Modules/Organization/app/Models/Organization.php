<?php

namespace Modules\Organization\app\Models;

use Khaleds\Shared\Models\BaseModel;
use App\Shared\HasRelationshipChecks;
use Modules\Property\app\Models\Property;
use Modules\Tenancy\Traits\BelongsToTenancy;
use Modules\BankAccount\Traits\HasBankAccounts;
use Modules\Property\app\Models\PropertyOwners;
use Modules\Lease\app\Models\Lease;

class Organization extends BaseModel
{
    use HasRelationshipChecks,HasBankAccounts;

    /**
     * The attributes that are not mass assignable.
     *
     * @var array
     */
    protected $guarded = ['id'];

    protected $translatable = [];

    protected $relationsList = ["properties","leases"];

    public function properties()
    {
        return $this->morphToMany(
            Property::class, 
            'ownerable', 
            'property_owners', 
            'ownerable_id', 
            'property_id'
        );
    }

    public function propertyOwners()
    {
        return $this->morphMany(PropertyOwners::class, 'ownerable');
    }

    public function leases()
    {
        return $this->belongsToMany(
            Lease::class,
            'lease_members',
            'member_id',
            'lease_id'
        )->where('member_type', 'organization');
    }

    public function invoices()
    {
        //returns not accurate data so i had used getTableQuery in the InvoiceRelationManager if you want to use in the code test the result returned first
        return $this->hasMany(\Modules\Invoice\app\Models\Invoice::class, 'for_id')
            ->where('for_type', self::class)
            ->whereIn('extra->lease_id', $this->leases()->pluck('leases.id'));
    }
}
