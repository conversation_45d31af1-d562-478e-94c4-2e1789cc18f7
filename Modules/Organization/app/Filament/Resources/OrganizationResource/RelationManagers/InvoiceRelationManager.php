<?php

namespace Modules\Organization\app\Filament\Resources\OrganizationResource\RelationManagers;

use Filament\Tables;
use Filament\Tables\Table;
use Modules\Lease\Enums\LeaseEnum;
use Illuminate\Database\Eloquent\Model;
use Modules\Invoice\Enums\InvoiceTypeEnum;
use Modules\Invoice\Enums\InvoiceStatusEnum;
use Filament\Resources\RelationManagers\RelationManager;
use Modules\Invoice\app\Models\Invoice;
use Filament\Tables\Actions\ViewAction;
use Illuminate\Support\Facades\DB;
use Modules\Invoice\app\Filament\Resources\InvoiceResource\Pages\CreateInvoice;
use Modules\Invoice\app\Filament\Resources\InvoiceResource\Pages\ListInvoices;
use Modules\Invoice\app\Filament\Resources\InvoiceResource\Pages\ViewInvoice;
use Modules\Payment\app\Filament\Resources\InvoiceResource\Actions\PayInvoiceAction;
use Illuminate\Support\HtmlString;

class InvoiceRelationManager extends RelationManager
{
    protected static string $relationship = 'invoices';

    public static function getTitle(Model $ownerRecord, string $pageClass): string
    {
        return __('Invoices');
    }

    protected static function getPluralRecordLabel(): ?string
    {
        return __('Invoices');
    }

    protected function getTableQuery(): \Illuminate\Database\Eloquent\Builder
    {
        $leaseIds = $this->ownerRecord->leases->pluck('id');

        // Custom query to fetch invoices based on JSON column condition
        return Invoice::query()
        ->whereIn(
            DB::raw("JSON_UNQUOTE(JSON_EXTRACT(extra, '$.lease_id'))"),
            $leaseIds
        )
        ->orderBy('created_at', 'desc');
    }
    protected static ?string $recordTitleAttribute = 'uuid';

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label(__('Id'))
                    ->sortable(),
                Tables\Columns\TextColumn::make('ejar_number')
                    ->label(__('Invoice Number'))
                    ->sortable(),
                Tables\Columns\TextColumn::make('invoice_type')
                    ->label(__('Invoice Type'))
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        InvoiceTypeEnum::SCHEDULE => 'info',
                        InvoiceTypeEnum::COMMISSION => 'warning',
                        InvoiceTypeEnum::INSURANCE => 'success',
                        InvoiceTypeEnum::MAINTENANCE_REQUEST => 'danger',
                        InvoiceTypeEnum::TERMINATION_CLOSE_REQUEST => 'danger',
                        default => 'gray',
                    })
                    ->sortable(),
                Tables\Columns\TextColumn::make('total')
                    ->label(__('Total'))
                    ->formatStateUsing(function ($state) {
                        return new HtmlString(
                            '<div class="">' .
                            number_format($state) .
                            ' <span class="icon-saudi_riyal"></span>' .
                            '</div>'
                       );
                    })
                    ->sortable(),
                Tables\Columns\TextColumn::make('remaining')
                    ->label(__('Remaining'))
                    ->formatStateUsing(function ($state) {
                        return new HtmlString(
                            '<div class="">' .
                            number_format($state) .
                            ' <span class="icon-saudi_riyal"></span>' .
                            '</div>'
                       );
                    })
                    ->sortable(),
                Tables\Columns\TextColumn::make('paid')
                    ->label(__('Paid'))
                    ->formatStateUsing(function ($state) {
                        return new HtmlString(
                            '<div class="">' .
                            number_format($state) .
                            ' <span class="icon-saudi_riyal"></span>' .
                            '</div>'
                       );
                    })
                    ->sortable(),
                Tables\Columns\TextColumn::make('status')
                    ->label(__('Status'))
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        InvoiceStatusEnum::PAID => 'success',
                        InvoiceStatusEnum::PARTIAL_PAID => 'warning',
                        InvoiceStatusEnum::UNPAID => 'danger',
                        InvoiceStatusEnum::SETTLED => 'info',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('due_date')
                    ->label(__('Due Date'))
                    ->dateTime('d-m-Y')  // Format date as dd-mm-yyyy
                    ->sortable(),

                Tables\Columns\TextColumn::make('release_date')
                    ->label(__('Release Date'))
                    ->dateTime('d-m-Y')  // Format date as dd-mm-yyyy
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('Created at'))
                    ->dateTime('d-m-Y H:i')  // Format date and time as dd-mm-yyyy hh:mm
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->defaultSort('created_at', 'desc')
            ->filters([
                Tables\Filters\SelectFilter::make('invoice_type')
                    ->label(__('Invoice Type'))
                    ->options(InvoiceTypeEnum::getInvoiceTypeOptions()),
                Tables\Filters\SelectFilter::make('status')
                    ->label(__('Status'))
                    ->options(InvoiceStatusEnum::getInvoiceStatusOptions()),
            ])
            ->headerActions([])
            ->actions([
                ViewAction::make()
                ->url(fn (Model $record) => url('/admin/invoices/' . $record->id . '/show')), // Custom URL for the ViewAction
                PayInvoiceAction::make()
                ])
            ->bulkActions([]);
    }
    public static function getPages(): array
    {
        return [
            'index' => ListInvoices::route('/'),
            'create' => CreateInvoice::route('/create'),
            'view' => ViewInvoice::route('/{record}/view'),
        ];
    }

    public static function canViewForRecord(Model $ownerRecord, string $pageClass): bool
    {
        return $ownerRecord?->status == LeaseEnum::DRAFT ? false : true;
    }
}
