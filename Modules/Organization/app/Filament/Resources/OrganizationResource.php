<?php

namespace Modules\Organization\app\Filament\Resources;

use Modules\Organization\app\Filament\Resources\OrganizationResource\Pages;
use Modules\Organization\app\Models\Organization;
use Filament\Forms;
use Filament\Forms\Components\Section;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Forms\Components\TextInput;
use Modules\BankAccount\Forms\BankAccountForm;
use Filament\Forms\Components\Actions\Action;
use Illuminate\Support\Facades\DB;
use Filament\Notifications\Notification;
use Modules\Organization\app\Filament\Resources\OrganizationResource\RelationManagers\InvoiceRelationManager;
use Modules\Organization\app\Filament\Resources\OrganizationResource\RelationManagers\LeaseRelationManager;
use Modules\Organization\app\Filament\Resources\OrganizationResource\RelationManagers\PropertiesRelationManager;

class OrganizationResource extends Resource
{
    protected static ?string $model = Organization::class;
    protected static ?string $navigationGroup = 'Filament Shield';

    protected static ?string $navigationIcon = 'heroicon-o-building-library';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make(__('Organization Details'))
                    ->columns(2)
                    ->schema([
                        TextInput::make('name')
                            ->label(__('Organization Name'))
                            ->placeholder(__('Enter organization name'))
                            ->required()
                            ->validationMessages([
                                'required' => __('Organization Name is required'),
                            ]),
                        TextInput::make('unified_number')
                            ->label(__('Unified Number'))
                            ->numeric()
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->placeholder('Just Start with 70xxxxxxxx')
                            ->extraInputAttributes([
                                'maxlength' => '10',
                                'minlength' => '10',
                                'pattern' => '70[0-9]{8}',
                                'oninput' => "
                                    this.value = this.value.replace(/[^0-9]/g, '').substring(0, 10);
                                    if (this.value.length > 1 && !this.value.startsWith('70')) {
                                        this.value = '70';
                                    }
                                    this.dispatchEvent(new Event('input'));
                                "
                            ])
                            ->rules([
                                'required',
                                'numeric',
                                'digits:10',
                                'regex:/^70\d{8}$/',
                            ])
                            ->validationMessages([
                                'required' => __('Unified Number is required'),
                                'digits' => __('Unified Number must be exactly 10 digits'),
                                'numeric' => __('Unified Number must contain only numbers'),
                                'regex' => __('Unified Number must start with 70'),
                                'unique' => __('Unified Number is already taken'),
                            ]),
                        TextInput::make('vat_number')
                            ->label(__('Vat Number'))
                            ->numeric()
                            ->default(0)
                            ->minValue(0)
                            ->required()
                            ->live(true)
                            ->afterStateUpdated(function ($set, $state) {
                                if ($state == null) {
                                    $set('vat_number', 0);
                                }
                            })
                            ->validationMessages([
                               'required' => __('Vat Number is required'),
                            ]),
                        TextInput::make('registration_number')
                            ->label(__('Registration Number'))
                            ->placeholder(__('Enter Registration Number'))
                            ->required()
                            ->validationMessages([
                                'required' => __('Registration Number is required'),
                            ]),
                        Forms\Components\DatePicker::make('registration_date')
                            ->label(__('Registration Date'))
                            ->placeholder(__('Enter Registration Date'))
                            ->required()
                            ->before(now())
                            ->native(false)
                            ->validationMessages([
                                'required' => __('Registration Date is required'),
                            ]),
                    ]),
            Forms\Components\Card::make()
                ->schema([
                    Forms\Components\Repeater::make('bankAccounts')
                        ->label(__('Bank Accounts'))
                        ->relationship('bankAccounts')
                        ->schema(BankAccountForm::make())
                        ->columnSpan('full')
                        ->deleteAction(
                            fn(Action $action) => $action
                                ->requiresConfirmation()
                                ->modalHeading(__('Delete Bank Account'))
                                ->modalDescription(__('Are you sure you want to delete this bank account?'))
                                ->before(function ($record, $state, $arguments) use ($action) {
                                    if (isset($state[$arguments['item']]['id'])) {
                                        $isInUse = DB::table('lease_members')
                                            ->where('bank_account_id', $state[$arguments['item']]['id'])
                                            ->whereExists(function ($query) {
                                                $query->select(DB::raw(1))
                                                    ->from('leases')
                                                    ->whereRaw('leases.id = lease_members.lease_id')
                                                    ->whereNull('leases.deleted_at');
                                            })
                                            ->exists();

                                        if ($isInUse) {
                                            Notification::make()
                                                ->danger()
                                                ->title(__('Delete Failed'))
                                                ->body(__('This bank account is currently in use by an active lease and cannot be deleted.'))
                                                ->send();
                                            $action->cancel();
                                        }
                                    }
                                })
                        )
                ])
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label(__('Name'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('unified_number')
                    ->label(__('Unified Number'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('registration_number')
                    ->label(__('Registration Number'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('Created at'))
                    ->since()
                    ->tooltip(fn($record) => $record->created_at->format('Y-m-d H:i:s')) // Display full date and time on hover
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('Updated at'))
                    ->since()
                    ->tooltip(fn($record) => $record->updated_at->format('Y-m-d H:i:s')) // Display full date and time on hover
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                //
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            LeaseRelationManager::class,
            PropertiesRelationManager::class,
            InvoiceRelationManager::class
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListOrganizations::route('/'),
            'create' => Pages\CreateOrganization::route('/create'),
            'edit' => Pages\EditOrganization::route('/{record}/edit'),
            'view' => Pages\ViewOrganization::route('/{record}'),
        ];
    }


    public static function getNavigationLabel(): string
    {
        return __("organizations"); // TODO: Change the autogenerated stub
    }
    public static function getNavigationGroup(): ?string
    {
        return __('filament-shield::filament-shield.nav.group');
    }

    public static function getBreadcrumb() : string
    {
        return __('organizations');
    }
    public static function getModelLabel(): string
    {
        return __('Organization');
    }

    public static function getPluralModelLabel(): string
    {
        return __('organizations');
    }
}
