<?php

namespace Modules\Company\app\Filament\Resources\CompanyResource\Pages;

use Modules\Company\app\Filament\Resources\CompanyResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Modules\Company\app\Filament\Resources\CompanyResource\Widgets\CompanyListOverview;

class ListCompanies extends ListRecords
{
    protected static string $resource = CompanyResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label(__('New Company')),
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            CompanyListOverview::class,
        ];
    }
}
