<?php

namespace Modules\Company\app\Filament\Resources\CompanyResource\Pages;

use App\Enums\RoleEnum;
use App\Enums\SyncUserAccountSourceEnum;
use App\Events\SyncUserAccountEvent;
use Filament\Notifications\Notification;;
use Illuminate\Support\Facades\DB;
use Modules\Company\app\Filament\Resources\CompanyResource;
use Filament\Resources\Pages\CreateRecord;
use App\Models\User;
use Modules\Company\app\Models\Company;
use Modules\Company\app\Models\UserProfile;
use Modules\Subscription\app\Events\UserRegistered;
use Illuminate\Validation\ValidationException;

class CreateCompany extends CreateRecord
{
    protected static string $resource = CompanyResource::class;
    protected $userPassword = null;
    protected User|null $companyUser = null;

    public function create(bool $another = false): void
    {
        try {
            DB::beginTransaction();

            $data = $this->form->getState();

            if (!isset($data['is_existing_user']) || !$data['is_existing_user']) {
                $userData = $data['user'];

                // Generate full name
                $userData['name'] = trim(implode(' ', array_filter([
                    $userData['userProfile']['first_name'] ?? '',
                    $userData['userProfile']['second_name'] ?? '',
                    $userData['userProfile']['third_name'] ?? '',
                    $userData['userProfile']['last_name'] ?? ''
                ])));

                // Set user data
                $userPassword = $userData['password'] ?? null;
                $userProfileData = $userData['userProfile'] ?? [];

                $userData['active'] = 1;
                $userData['email_verified'] = 1;
                $userData['email_verified_at'] = now();
                unset($userData['userProfile']);

                // Create user
                $user = User::create($userData);

                // Create user profile
                if ($userProfileData) {
                    $userProfile = new UserProfile($userProfileData);
                    $userProfile->user_id = $user->id;
                    $userProfile->save();
                }

                // Assign role
                $user->assignRole(RoleEnum::OWNER);

            } else {
                $user = User::findOrFail($data['owner_id']);
            }

            // Create company
            $company = Company::create([
                'name' => $data['name'],
                'phone' => $data['phone'],
                'email' => $data['email'],
                'address' => $data['address'],
                'comp_unified_number' => $data['comp_unified_number'],
                'comp_cr_number' => $data['comp_cr_number'],
                'website_url' => $data['website_url'] ?? null,
                'logo' => $data['logo'] ?? null,
                'comp_unified_file' => $data['comp_unified_file'] ?? null,
                'user_id' => $user->id
            ]);

            // Attach user to company
            $company->usersWithPivot()->attach($user->id);

            // Handle owner session
            if (auth()->user()->hasRole(RoleEnum::OWNER)) {
                // Update user company
                $user->update(['company_id' => $company->id]);
                session()->put('userTenancy', User::find(auth()->user()->id));
            }

            // Trigger events for new users
            if (!isset($data['is_existing_user']) || !$data['is_existing_user']) {
                $user->update([
                    'company_id' => $company->id,
                ]);
                event(new UserRegistered($user));
                $user->unhashed_password = $userPassword;
                event(new SyncUserAccountEvent($user, SyncUserAccountSourceEnum::ADD_USER_COMPANY->value));
            }

            DB::commit();

            Notification::make()
                ->title(__('Success'))
                ->body(__('Company created successfully.'))
                ->success()
                ->send();

            $this->redirect($this->getRedirectUrl());

        } catch (ValidationException $e) {
                DB::rollBack();

                // Get all validation errors as a string
                $errors = collect($e->validator->errors()->all())->join("\n");

                Notification::make()
                    ->title(__('Validation Error'))
                    ->body($errors)
                    ->danger()
                    ->send();
            } catch (\Exception $e) {
                DB::rollBack();

                Notification::make()
                    ->title(__('Error'))
                    ->body(__('Failed to create company. Please try again.'))
                    ->danger()
                    ->send();
        }
    }



    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
