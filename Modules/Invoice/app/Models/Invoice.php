<?php

namespace Modules\Invoice\app\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Khaleds\Shared\Models\BaseModel;
use Modules\Company\app\Models\Company;
use Modules\Invoice\Services\Resolvers\PropertyNameResolver;
use Modules\Lease\app\Models\Lease;
use Modules\Invoice\app\Models\InvoiceSchedule;
use Modules\Invoice\Enums\InvoiceTypeEnum;
use Modules\Lease\app\Models\LeaseCommission;
use Modules\Payment\app\Models\KhaledsPayment;
use Modules\Payment\Enums\PaymentStatusEnum;
use Modules\Request\app\Models\Request;
use Modules\Tenancy\Traits\BelongsToTenancy;

class Invoice extends BaseModel
{
    use SoftDeletes;
    use BelongsToTenancy;

    protected $table = 'invoices';

    /**
     * The attributes that are not mass assignable.
     *
     * @var array
     */
    protected $guarded = ['id'];

    protected static function boot()
    {
        parent::boot();

        // static::creating(function ($model) {
        //     if(!is_null(auth()->user()->company_id)){
        //         $model->company_id = auth()->user()->company_id;
        //     }
        // });
    }
    /**
     * Get the model that the invoice is for (polymorphic relation).
     */
    public function forable()
    {
        return $this->morphTo('for');
    }

    /**
     * Get the model that the invoice is from (polymorphic relation).
     */
    public function fromable()
    {
        return $this->morphTo('from');
    }

    /**
     * Example of another possible relationship with invoice items
     * if your invoice has items linked in another table.
     */
    public function items()
    {
        return $this->hasMany(InvoiceItem::class);
    }

    public function invoice_schedule()
    {
        return $this->hasOne(InvoiceSchedule::class, 'invoice_id');
    }

    // Then in your Invoice model:
    public function property_name()
    {
        return app(PropertyNameResolver::class)->resolve($this);
    }

    // Then in your Invoice model:
    public function payments(): HasMany
    {
        return $this->hasMany(KhaledsPayment::class, 'order_id')
            ->where('order_table', self::class)->where('payment_status' , PaymentStatusEnum::PAID);
    }

    public function company()
    {
        return $this->hasOne(Company::class,'id','company_id');
    }

    public function allPayments()
    {
        return $this->morphMany(KhaledsPayment::class, 'order','order_table','order_id');
    }

    private function getLeaseFromExtra(): ?Lease
    {
        $extra = json_decode($this->extra, true);
        $leaseId = $extra['lease_id'] ?? null;

        return $leaseId ? Lease::find($leaseId) : null;
    }

    // Now update lease_units()
    public function lease_units()
    {
        $lease = $this->getLeaseFromExtra();

        if (!$lease || $lease->units->isEmpty()) {
            return '';
        }

        return $lease->units
            ->map(function ($unit) {
                return $unit->property_type->name . ' - ' . $unit->number;
            })
            ->implode(' , ');
    }

    // And update lease_tenant()
    public function lease_tenant()
    {
        $lease = $this->getLeaseFromExtra();
        return $lease?->tenant;
    }

    public function getLeaseNumberAttribute(): ?string
    {
        $extra = json_decode($this->extra, true);
        $leaseId = $extra['lease_id'] ?? null;

        if ($leaseId) {
            return Lease::find($leaseId)?->lease_number;
        }

        return null;
    }

    public function najzRequests(): HasMany
    {
        return $this->hasMany(NajzRequest::class, 'invoice_id');
    }

    public function najzRequest(): HasOne
    {
        return $this->hasOne(NajzRequest::class, 'invoice_id');
    }

    /**
     * Scope for custom search functionality
     */
    public function scopeCustomSearch($query, $search)
    {
        if (filled($search)) {
            $query->where(function ($query) use ($search) {
                // Search in basic invoice fields
                $query->where('id', 'like', "%{$search}%")
                    ->orWhere('ejar_number', 'like', "%{$search}%")
                    ->orWhere('for_id', 'like', "%{$search}%")
                    ->orWhere('status', 'like', "%{$search}%")
                    ->orWhere('total', 'like', "%{$search}%")
                    ->orWhere('remaining', 'like', "%{$search}%")
                    // Search in JSON extra field for lease_id
                    ->orWhereRaw('json_unquote(json_extract(`extra`, \'$."lease_id"\')) like ?', ["%{$search}%"])
                    // Search by lease number using subquery
                    ->orWhereExists(function ($subQuery) use ($search) {
                        $subQuery->select(\DB::raw(1))
                            ->from('leases')
                            ->whereRaw('leases.id = JSON_UNQUOTE(JSON_EXTRACT(invoices.extra, "$.lease_id"))')
                            ->where('leases.lease_number', 'like', "%{$search}%");
                    })
                    // Search by national_id of lease members using subquery
                    ->orWhereExists(function ($subQuery) use ($search) {
                        $subQuery->select(\DB::raw(1))
                            ->from('leases')
                            ->join('lease_members', 'leases.id', '=', 'lease_members.lease_id')
                            ->join('accounts', function ($join) {
                                $join->on('lease_members.member_id', '=', 'accounts.id')
                                     ->where('lease_members.memberable_type', '=', \Modules\Account\app\Models\Account::class);
                            })
                            ->whereRaw('leases.id = JSON_UNQUOTE(JSON_EXTRACT(invoices.extra, "$.lease_id"))')
                            ->where('accounts.national_id', 'like', "%{$search}%");
                    });
            });
        }
        return $query;
    }
}
