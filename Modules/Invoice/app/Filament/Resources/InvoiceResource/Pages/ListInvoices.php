<?php

namespace Modules\Invoice\app\Filament\Resources\InvoiceResource\Pages;

use Modules\Invoice\app\Filament\Resources\InvoiceResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Modules\Invoice\app\Filament\Resources\InvoiceResource\Widgets\InvoiceListOverview;
use Illuminate\Database\Eloquent\Builder;

class ListInvoices extends ListRecords
{
    protected static string $resource = InvoiceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label(__('Add Invoice')),
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            InvoiceListOverview::class,
        ];
    }

    protected function applySearchToTableQuery(Builder $query): Builder
    {
        $search = $this->getTableSearch();

        if (filled($search)) {
            // custom search which includes all the advanced search logic
            $query->customSearch($search);
        }

        return $query;
    }

}
