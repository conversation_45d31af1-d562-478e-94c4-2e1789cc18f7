<?php
namespace Modules\EjarIntegration\Factories;

use AllowDynamicProperties;
use Modules\Property\app\Models\Property;
use Modules\Property\app\Models\SyncPropertyStep;

#[AllowDynamicProperties]
class SyncPropertyFactory
{
    public function __construct(Property $property, SyncPropertyStep $step)
    {
        $this->property = $property;
        $this->step = $step;
    }

    public function make(string $stepClass)
    {
        //resolve property step class
        $className = 'Modules\EjarIntegration\PropertySynchronizationSteps\\' . $stepClass;

        if (class_exists($className)) {
            return new $className($this->property, $this->step);
        }
        throw new \Exception(__("Invalid sync step"));
    }
}
