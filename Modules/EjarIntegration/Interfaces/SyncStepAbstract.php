<?php
namespace Modules\EjarIntegration\Interfaces;

use AllowDynamicProperties;
use Modules\EjarIntegration\app\Helpers\EjarHttpHelper;
use Modules\EjarIntegration\Traits\HasCompanyKeys;
use Modules\Property\app\Models\Property;
use Modules\Property\app\Models\SyncPropertyStep;

#[AllowDynamicProperties]
abstract class SyncStepAbstract
{
    use EjarHttpHelper, HasCompanyKeys;

    protected string $base_url;
    protected string $clientId;
    protected string $clientSecret;

    protected array $response = ['status' => true, 'message' => null, 'data' => []];

    /**
     * @throws \Exception
     */
    public function __construct(Property $property, SyncPropertyStep $step)
    {
        $this->property = $property;
        $this->step = $step;
        $this->base_url = "https://test.kera.sa/nhc/uat/v1/ejar/ecrs/"; //todo get from config
        $this->setCompanyKeysByProperty($this->property);
        $this->setHttpHeaders([
            "X-IBM-Client-Id" => $this->clientId,
            "X-IBM-Client-Secret" => $this->clientSecret,
            "RefId" => "1"
        ]);
    }
}
