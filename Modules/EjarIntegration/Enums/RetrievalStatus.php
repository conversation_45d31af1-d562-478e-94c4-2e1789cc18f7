<?php
namespace Modules\EjarIntegration\Enums;

enum RetrievalStatus: string
{
    case STARTED = 'started';
    case COMPLETED = 'completed';
    case FAILED = 'failed';

    public static function values(): array
    {
        return array_map(fn($case) => $case->value, self::cases());
    }

    public function getLabel(): string
    {
        return match($this) {
            self::STARTED => __('Started'),
            self::COMPLETED => __('Completed'),
            self::FAILED => __('Failed'),
        };
    }
}
