<?php
namespace Modules\EjarIntegration\PropertySynchronizationSteps;

use Modules\Account\app\Models\Account;
use Modules\EjarIntegration\Interfaces\ISyncInterface;
use Modules\EjarIntegration\Interfaces\SyncStepAbstract;
use Modules\Organization\app\Models\Organization;
use Modules\Property\app\Models\PropertyOwners;

Class SyncIBANs extends SyncStepAbstract implements ISyncInterface
{
    public function sync(): array
    {
        try {
            $owners = $this->property->owners;
            if ($owners->isEmpty()) {
                $this->response['status'] = false;
                $this->response['message'] = __("There is no owner associated with this property");

                return $this->response;
            }
            $collectRes = [];
            foreach ($owners as $owner) {
                $entityUUID = $this->getEntityUUID($owner);
                if (is_null($entityUUID)) {
                    $this->response['status'] = false;
                    $this->response['message'] = __("You should sync the owners before trying to sync IBANs");
                    return $this->response;
                }
                $url = $this->getSyncIBANUrl($owner, $entityUUID);
                $ownerBankAccounts = $owner->ownerable->bankAccounts;
                if ($ownerBankAccounts->isEmpty()) {
                    $this->response['status'] = false;
                    $this->response['message'] = __("There is no bank account associated with ") . $owner->ownerable->name ?? __("this owner") . __(" for this property");
                    return $this->response;
                }
                foreach ($ownerBankAccounts as $bankAccount) {
                    $multipartRequestData = [
                        [
                            'name' => 'data[attributes][iban_number]',
                            'contents' => $bankAccount->iban ?? null
                        ],
                        [
                            'name' => 'data[attributes][bank_name]',
                            'contents' => $bankAccount->bank_name ?? null
                        ],
                        [
                            'name' => 'data[attributes][alias_name]',
                            'contents' => 'IBAN - ' . ($bankAccount->account_name ?? null)
                        ],
                        [
                            'name' => 'data[attributes][swift_code]',
                            'contents' => $bankAccount->swift_code ?? null
                        ],
                        [
                            'name' => 'data[attributes][bank_address]',
                            'contents' => $bankAccount->bank_address ?? null
                        ],
                    ];
                    $this->postMultipartForm($url, $multipartRequestData);
                    $res = json_decode($this->httpResponse['data'], true);


                    if ($this->httpResponse['code'] == 201 || $this->httpResponse['code'] == 200) {
                        //syncing data
                        $bankAccount->ejar_id = $res['Body']['data']['id'];
                        $bankAccount->ejar_legal_status = $res['Body']['data']['attributes']['legal'];
                        $bankAccount->save();
                        $collectRes[] = $res;
                    } else {
                        $this->response['status'] = false;
                        $this->response['data'] = $res;
                        if (isset($res['Body']['errors']) && count($res['Body']['errors']) > 0) {
                            $this->response['message'] = $res['Body']['errors'][0]['detail'][app()->getLocale()];
                        } else {
                            $this->response['message'] = __("Something went wrong, please try again later.");
                        }
                        return $this->response;
                    }
                }
            }
            $this->response['message'] = __("IBAN accounts has been synchronized");
            $this->response['data'] = $collectRes;
        } catch (\Exception $e) {
            $this->response['status'] = false;
            $this->response['message'] =$e->getMessage();
        }

        return $this->response;
    }

    /**
     * @throws \Exception
     */
    protected function getSyncIBANUrl(PropertyOwners $owner, string $entityUUID): string
    {
        if ($owner->ownerable_type == Organization::class) {
            $url = $this->base_url . 'PostOrganizationIBAN?OrganizationUUID=' . $entityUUID;
        } elseif ($owner->ownerable_type == Account::class) {
            $url = $this->base_url . 'PostIndividualIBAN?IndividualUUID=' . $entityUUID ;//62e60634-0644-469d-b10d-fe574281fbdb;
        } else {
            throw new \Exception(__("Undefined entity type"));
        }
        return $url;
    }

    /**
     * @throws \Exception
     */
    protected function getEntityUUID(PropertyOwners $owner)
    {
        return $owner->ownerable->ejar_uuid ?? null;
    }

}
