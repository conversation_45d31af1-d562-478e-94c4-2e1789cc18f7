<?php
namespace Modules\EjarIntegration\PropertySynchronizationSteps;

use GeniusTS\HijriDate\Hijri;
use Modules\Account\app\Models\Account;
use Modules\EjarIntegration\Enums\EjarIDTypes;
use Modules\EjarIntegration\Interfaces\ISyncInterface;
use Modules\EjarIntegration\Interfaces\SyncStepAbstract;
use Modules\Organization\app\Models\Organization;
use Modules\Property\app\Models\PropertyOwners;

Class SyncIndividualEntities extends SyncStepAbstract implements ISyncInterface
{
    public function sync(): array
    {
        try {
            $individualEntityUrl = $this->base_url . 'PostIndividualEntity';
            $orgEntityUrl = $this->base_url . 'GetOrganizationEntity';
            $owners = $this->property->owners;
            if ($owners->isEmpty()) {
                $this->response['status'] = false;
                $this->response['message'] = __("There is no owner associated with this property");

                return $this->response;
            }
            $collectRes = [];
            foreach ($owners as $owner) {
                $requestData = $this->getRequestData($owner);
                if ($owner->ownerable_type == Organization::class) {
                    $this->get($orgEntityUrl, $requestData);
                } else {
                    $this->post($individualEntityUrl, $requestData);
                }
                $res = json_decode($this->httpResponse['data'], true);
                if ($this->httpResponse['code'] == 200) {
                    if ($owner->ownerable_type == Account::class) {
                        $owner->ownerable->update([
                            'ejar_uuid' => $res['Body']['data']['id'],
                            'ejar_verified' => $res['Body']['data']['attributes']['verification_status'] == 'verification_succeed' ? 1 : 0,
                        ]); //syncing data with our system
                    } elseif ($owner->ownerable_type == Organization::class && !is_null($res['Body'])) {
                        $owner->ownerable->update([
                            'name' => $res['Body']['data']['attributes']['name'],
                            'ejar_uuid' => $res['Body']['data']['id'],
                        ]);
                    } else {
                        $this->response['status'] = false;
                        $this->response['data'] = $res;
                        $this->response['message'] = __("The organization associated with this property does not verified.");
                        return $this->response;
                    }
                    $collectRes[] = $res;
                } else {
                    $this->response['status'] = false;
                    $this->response['data'] = $res;
                    if (isset($res['Body']['errors']) && count($res['Body']['errors']) > 0) {
                        $this->response['message'] = $res['Body']['errors'][0]['detail'][app()->getLocale()];
                    } else {
                        $this->response['message'] = __("Something went wrong, please try again later.");
                    }
                    return $this->response;
                }
            }

            $this->response['message'] = __("Entities has been synchronized");
            $this->response['data'] = $collectRes;
        } catch (\Exception $e) {
            $this->response['status'] = false;
            $this->response['message'] =$e->getMessage();
        }
        return $this->response;
    }

    protected function getRequestData(PropertyOwners $owner): array {
        if ($owner->ownerable_type == Organization::class) {
            $requestData = [
                'registration_number' => $owner->ownerable->registration_number ?? null,
                'registration_date' => $owner->ownerable->registration_date ?? null,
            ];
        } else {
            $requestData = [
                'id_number' => $owner->ownerable->national_id ?? null,
                "id_type" => EjarIDTypes::NATIONAL_ID->value, //only national id in this time
                "date_of_birth_hijri" => Hijri::convertToHijri($owner->ownerable->birth_date ?? null)->format('Y-m-d') //convert to hijri date
            ];
        }
        return $requestData;
    }

}
