<?php

namespace Modules\Notification\app\Filament\Resources\UserNotificationResource\Pages;

use Modules\Notification\app\Filament\Resources\UserNotificationResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditUserNotification extends EditRecord
{
    protected static string $resource = UserNotificationResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
